<template>
  <div class="variable-item bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div :class="typeIconClasses">
              {{ typeIcon }}
            </div>
          </div>
          
          <div class="flex-1 min-w-0">
            <h3 class="text-sm font-medium text-gray-900 truncate">
              {{ variable.name }}
            </h3>
            <p class="text-xs text-gray-500 mt-1">
              Type: {{ variable.type }}
              <span v-if="variable.description" class="ml-2">
                | {{ variable.description }}
              </span>
            </p>
          </div>
        </div>
        
        <!-- Variable-specific display -->
        <div class="mt-3">
          <VariableValueDisplay :variable="variable" />
        </div>
      </div>
      
      <div class="flex items-center space-x-2 ml-4">
        <!-- Current value display -->
        <div class="text-right">
          <div class="text-sm font-medium text-gray-900">
            {{ formatValue(getCurrentValue()) }}
          </div>
          <div v-if="showLastUpdated" class="text-xs text-gray-400">
            {{ formatDate(variable.updatedAt) }}
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center space-x-1">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleEdit"
            title="Edit variable"
          >
            ✏️
          </BaseButton>
          
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleDuplicate"
            title="Duplicate variable"
          >
            📋
          </BaseButton>
          
          <BaseButton
            variant="danger"
            size="sm"
            @click="handleDelete"
            title="Delete variable"
          >
            ×
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Variable } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'
import VariableValueDisplay from './VariableValueDisplay.vue'

export interface VariableItemProps {
  variable: Variable
  showLastUpdated?: boolean
}

const props = withDefaults(defineProps<VariableItemProps>(), {
  showLastUpdated: false
})

const emit = defineEmits<{
  edit: [variable: Variable]
  duplicate: [variable: Variable]
  delete: [variable: Variable]
}>()

const typeIcon = computed(() => {
  const icons: Record<string, string> = {
    'number': '🔢',
    'text': '📝',
    'percentage': '%',
    'currency': '💰',
    'boolean': '✓',
    'multi-select': '☰',
    'range': '📊',
    'predicate': '🔀'
  }
  return icons[props.variable.type] || '❓'
})

const typeIconClasses = computed(() => [
  'w-8 h-8 rounded-full flex items-center justify-center text-sm',
  getTypeColor(props.variable.type)
])

function getTypeColor(type: string): string {
  const colors: Record<string, string> = {
    'number': 'bg-blue-100 text-blue-600',
    'text': 'bg-green-100 text-green-600',
    'percentage': 'bg-purple-100 text-purple-600',
    'currency': 'bg-yellow-100 text-yellow-600',
    'boolean': 'bg-indigo-100 text-indigo-600',
    'multi-select': 'bg-pink-100 text-pink-600',
    'range': 'bg-orange-100 text-orange-600',
    'predicate': 'bg-red-100 text-red-600'
  }
  return colors[type] || 'bg-gray-100 text-gray-600'
}

function getCurrentValue(): any {
  switch (props.variable.type) {
    case 'multi-select':
      return (props.variable as any).selectedValues || []
    case 'range':
      return (props.variable as any).value
    case 'predicate':
      return (props.variable as any).defaultValue
    default:
      return (props.variable as any).value || props.variable.defaultValue
  }
}

function formatValue(value: any): string {
  if (value === null || value === undefined) {
    return 'Not set'
  }
  
  if (Array.isArray(value)) {
    return value.length > 0 ? `${value.length} selected` : 'None selected'
  }
  
  if (typeof value === 'boolean') {
    return value ? 'True' : 'False'
  }
  
  if (props.variable.type === 'currency') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(value))
  }
  
  if (props.variable.type === 'percentage') {
    return `${Number(value)}%`
  }
  
  return String(value)
}

function formatDate(dateString?: string): string {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return date.toLocaleDateString()
}

function handleEdit() {
  emit('edit', props.variable)
}

function handleDuplicate() {
  emit('duplicate', props.variable)
}

function handleDelete() {
  if (confirm(`Are you sure you want to delete the variable "${props.variable.name}"?`)) {
    emit('delete', props.variable)
  }
}
</script>
