<template>
  <div class="expression-display">
    <!-- Expression Text -->
    <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
      <pre class="whitespace-pre-wrap">{{ formattedExpression }}</pre>
    </div>
    
    <!-- Validation Status -->
    <div class="mt-4 flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div :class="statusClasses">
          {{ statusIcon }}
        </div>
        <span :class="statusTextClasses">
          {{ statusText }}
        </span>
      </div>
      
      <div class="flex items-center space-x-2">
        <BaseButton
          variant="ghost"
          size="sm"
          @click="copyExpression"
          title="Copy expression to clipboard"
        >
          📋 Copy
        </BaseButton>
        
        <BaseButton
          variant="ghost"
          size="sm"
          @click="showDetails = !showDetails"
        >
          {{ showDetails ? 'Hide' : 'Show' }} Details
        </BaseButton>
      </div>
    </div>
    
    <!-- Detailed Information -->
    <div v-if="showDetails" class="mt-4 space-y-4">
      <!-- Errors -->
      <div v-if="errors.length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-red-800 mb-2">Errors</h4>
        <ul class="space-y-1">
          <li
            v-for="error in errors"
            :key="error.message"
            class="text-sm text-red-700 flex items-start"
          >
            <span class="text-red-500 mr-2">•</span>
            <div>
              <span class="font-medium">{{ error.type }}:</span>
              {{ error.message }}
              <span v-if="error.location" class="text-red-600 text-xs ml-1">
                ({{ formatLocation(error.location) }})
              </span>
            </div>
          </li>
        </ul>
      </div>
      
      <!-- Warnings -->
      <div v-if="warnings.length > 0" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-yellow-800 mb-2">Warnings</h4>
        <ul class="space-y-1">
          <li
            v-for="warning in warnings"
            :key="warning.message"
            class="text-sm text-yellow-700 flex items-start"
          >
            <span class="text-yellow-500 mr-2">•</span>
            <div>
              <span class="font-medium">{{ warning.type }}:</span>
              {{ warning.message }}
              <span v-if="warning.location" class="text-yellow-600 text-xs ml-1">
                ({{ formatLocation(warning.location) }})
              </span>
            </div>
          </li>
        </ul>
      </div>
      
      <!-- Expression Statistics -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-800 mb-2">Expression Statistics</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span class="text-blue-600 font-medium">Length:</span>
            <span class="ml-1">{{ expression.length }} characters</span>
          </div>
          <div>
            <span class="text-blue-600 font-medium">Complexity:</span>
            <span class="ml-1">{{ getComplexityLevel() }}</span>
          </div>
          <div>
            <span class="text-blue-600 font-medium">Conditions:</span>
            <span class="ml-1">{{ getConditionCount() }}</span>
          </div>
          <div>
            <span class="text-blue-600 font-medium">Variables:</span>
            <span class="ml-1">{{ getVariableCount() }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ExpressionError, ExpressionWarning } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'

export interface ExpressionDisplayProps {
  expression: string
  isValid: boolean
  errors: ExpressionError[]
  warnings: ExpressionWarning[]
}

const props = defineProps<ExpressionDisplayProps>()

const showDetails = ref(false)

const formattedExpression = computed(() => {
  if (!props.expression || props.expression === 'No conditions defined') {
    return '// No expression generated yet\n// Add variables and conditions to build your expression'
  }
  
  // Format the expression with proper indentation and line breaks
  return props.expression
    .replace(/IF\(/g, '\nIF(')
    .replace(/THEN/g, '\n  THEN')
    .replace(/ELSE/g, '\n  ELSE')
    .replace(/AND/g, '\nAND')
    .trim()
})

const statusIcon = computed(() => {
  if (props.isValid) return '✓'
  if (props.errors.length > 0) return '✗'
  if (props.warnings.length > 0) return '⚠'
  return 'ℹ'
})

const statusText = computed(() => {
  if (props.isValid && props.errors.length === 0 && props.warnings.length === 0) {
    return 'Expression is valid'
  }
  if (props.errors.length > 0) {
    return `${props.errors.length} error${props.errors.length !== 1 ? 's' : ''} found`
  }
  if (props.warnings.length > 0) {
    return `${props.warnings.length} warning${props.warnings.length !== 1 ? 's' : ''} found`
  }
  return 'Expression status unknown'
})

const statusClasses = computed(() => [
  'w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold',
  props.isValid && props.errors.length === 0 && props.warnings.length === 0
    ? 'bg-green-100 text-green-600'
    : props.errors.length > 0
    ? 'bg-red-100 text-red-600'
    : props.warnings.length > 0
    ? 'bg-yellow-100 text-yellow-600'
    : 'bg-blue-100 text-blue-600'
])

const statusTextClasses = computed(() => [
  'text-sm font-medium',
  props.isValid && props.errors.length === 0 && props.warnings.length === 0
    ? 'text-green-700'
    : props.errors.length > 0
    ? 'text-red-700'
    : props.warnings.length > 0
    ? 'text-yellow-700'
    : 'text-blue-700'
])

function formatLocation(location: any): string {
  const parts: string[] = []
  
  if (location.variableId) {
    parts.push(`Variable: ${location.variableId}`)
  }
  
  if (location.conditionId) {
    parts.push(`Condition: ${location.conditionId}`)
  }
  
  if (location.line) {
    parts.push(`Line: ${location.line}`)
  }
  
  if (location.column) {
    parts.push(`Column: ${location.column}`)
  }
  
  return parts.join(', ')
}

function getComplexityLevel(): string {
  const length = props.expression.length
  const conditionCount = (props.expression.match(/IF\(/g) || []).length
  
  if (length < 50 && conditionCount <= 1) return 'Simple'
  if (length < 200 && conditionCount <= 3) return 'Medium'
  if (length < 500 && conditionCount <= 5) return 'Complex'
  return 'Very Complex'
}

function getConditionCount(): number {
  return (props.expression.match(/IF\(/g) || []).length
}

function getVariableCount(): number {
  // This is a simplified count - in a real implementation you'd parse the expression properly
  const matches = props.expression.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || []
  const uniqueVariables = new Set(matches.filter(match => 
    !['IF', 'THEN', 'ELSE', 'AND', 'OR', 'NOT'].includes(match.toUpperCase())
  ))
  return uniqueVariables.size
}

async function copyExpression() {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(props.expression)
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = props.expression
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
      } finally {
        document.body.removeChild(textArea)
      }
    }
    
    // You could emit an event here to show a notification
    console.log('Expression copied to clipboard')
  } catch (error) {
    console.error('Failed to copy expression:', error)
  }
}
</script>
