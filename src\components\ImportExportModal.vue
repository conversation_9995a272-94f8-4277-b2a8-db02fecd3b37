<template>
  <BaseModal
    :model-value="modelValue"
    :title="mode === 'import' ? 'Import Expression' : 'Export Expression'"
    size="lg"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <!-- Import Mode -->
    <div v-if="mode === 'import'" class="space-y-4">
      <div class="form-group">
        <label class="form-label">Import Method</label>
        <BaseSelect
          v-model="importMethod"
          :options="importMethodOptions"
          placeholder="Select import method"
        />
      </div>
      
      <!-- File Upload -->
      <div v-if="importMethod === 'file'" class="form-group">
        <label class="form-label">Select JSON File</label>
        <input
          type="file"
          accept=".json"
          @change="handleFileSelect"
          class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
        />
      </div>
      
      <!-- JSON Text Input -->
      <div v-else-if="importMethod === 'text'" class="form-group">
        <label class="form-label">Paste JSON Content</label>
        <textarea
          v-model="jsonText"
          rows="10"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm font-mono"
          placeholder="Paste your JSON expression here..."
        />
      </div>
      
      <!-- Import Preview -->
      <div v-if="previewData" class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Import Preview:</h4>
        <div class="text-sm space-y-1">
          <div><span class="font-medium">Name:</span> {{ previewData.name }}</div>
          <div><span class="font-medium">Variables:</span> {{ previewData.variables?.length || 0 }}</div>
          <div><span class="font-medium">Conditions:</span> {{ previewData.conditions?.length || 0 }}</div>
          <div v-if="previewData.description"><span class="font-medium">Description:</span> {{ previewData.description }}</div>
        </div>
      </div>
      
      <!-- Import Errors -->
      <div v-if="importError" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-red-800 mb-1">Import Error:</h4>
        <p class="text-sm text-red-700">{{ importError }}</p>
      </div>
    </div>
    
    <!-- Export Mode -->
    <div v-else class="space-y-4">
      <div class="form-group">
        <label class="form-label">Export Format</label>
        <BaseSelect
          v-model="exportFormat"
          :options="exportFormatOptions"
          placeholder="Select export format"
        />
      </div>
      
      <div class="form-group">
        <label class="form-label">Export Method</label>
        <BaseSelect
          v-model="exportMethod"
          :options="exportMethodOptions"
          placeholder="Select export method"
        />
      </div>
      
      <!-- Export Preview -->
      <div v-if="exportPreview" class="form-group">
        <label class="form-label">Preview</label>
        <textarea
          :value="exportPreview"
          readonly
          rows="10"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 sm:text-sm font-mono"
        />
      </div>
    </div>

    <template #footer>
      <BaseButton
        variant="secondary"
        @click="$emit('update:modelValue', false)"
      >
        Cancel
      </BaseButton>
      
      <BaseButton
        v-if="mode === 'import'"
        variant="primary"
        @click="handleImport"
        :disabled="!canImport"
      >
        Import Expression
      </BaseButton>
      
      <div v-else class="flex space-x-2">
        <BaseButton
          variant="secondary"
          @click="handleCopyToClipboard"
          :disabled="!exportPreview"
        >
          Copy to Clipboard
        </BaseButton>
        <BaseButton
          variant="primary"
          @click="handleExport"
          :disabled="!exportPreview"
        >
          Download File
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useJsonImportExport } from '@/composables/useJsonImportExport'
import type { Expression } from '@/types'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'

export interface ImportExportModalProps {
  modelValue: boolean
  mode: 'import' | 'export'
  expression?: Expression
}

const props = defineProps<ImportExportModalProps>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  import: [expression: Expression]
  export: []
}>()

const jsonImportExport = useJsonImportExport()

// Import state
const importMethod = ref('file')
const jsonText = ref('')
const selectedFile = ref<File | null>(null)
const previewData = ref<any>(null)
const importError = ref<string | null>(null)

// Export state
const exportFormat = ref('full')
const exportMethod = ref('download')
const exportPreview = ref('')

const importMethodOptions = [
  { value: 'file', label: 'Upload JSON File' },
  { value: 'text', label: 'Paste JSON Text' }
]

const exportFormatOptions = [
  { value: 'full', label: 'Full (with schema)' },
  { value: 'minimal', label: 'Minimal (data only)' }
]

const exportMethodOptions = [
  { value: 'download', label: 'Download File' },
  { value: 'clipboard', label: 'Copy to Clipboard' }
]

const canImport = computed(() => {
  return previewData.value && !importError.value
})

// Watch for changes to generate export preview
watch([() => props.expression, exportFormat], () => {
  if (props.mode === 'export' && props.expression) {
    try {
      exportPreview.value = jsonImportExport.exportToJsonString(
        props.expression,
        exportFormat.value as 'full' | 'minimal',
        true
      )
    } catch (error) {
      exportPreview.value = ''
    }
  }
}, { immediate: true })

// Watch for import data changes
watch([importMethod, jsonText, selectedFile], () => {
  previewData.value = null
  importError.value = null
  
  if (importMethod.value === 'text' && jsonText.value) {
    try {
      const parsed = JSON.parse(jsonText.value)
      const validation = jsonImportExport.validateJsonStructure(parsed)
      
      if (validation.isValid) {
        previewData.value = parsed.expression
      } else {
        importError.value = validation.errors.join(', ')
      }
    } catch (error) {
      importError.value = 'Invalid JSON format'
    }
  }
})

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    selectedFile.value = file
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const parsed = JSON.parse(content)
        const validation = jsonImportExport.validateJsonStructure(parsed)
        
        if (validation.isValid) {
          previewData.value = parsed.expression
          importError.value = null
        } else {
          importError.value = validation.errors.join(', ')
          previewData.value = null
        }
      } catch (error) {
        importError.value = 'Invalid JSON file'
        previewData.value = null
      }
    }
    
    reader.readAsText(file)
  }
}

async function handleImport() {
  try {
    let expression: Expression
    
    if (importMethod.value === 'file' && selectedFile.value) {
      expression = await jsonImportExport.importFromFile(selectedFile.value)
    } else if (importMethod.value === 'text' && jsonText.value) {
      expression = jsonImportExport.importFromJsonString(jsonText.value)
    } else {
      return
    }
    
    emit('import', expression)
  } catch (error) {
    importError.value = error instanceof Error ? error.message : 'Import failed'
  }
}

function handleExport() {
  if (!props.expression) return
  
  try {
    jsonImportExport.downloadAsJson(
      props.expression,
      undefined,
      exportFormat.value as 'full' | 'minimal'
    )
    emit('export')
  } catch (error) {
    console.error('Export failed:', error)
  }
}

async function handleCopyToClipboard() {
  if (!props.expression) return
  
  try {
    await jsonImportExport.copyToClipboard(
      props.expression,
      exportFormat.value as 'full' | 'minimal'
    )
    // You could emit an event here to show a notification
  } catch (error) {
    console.error('Copy to clipboard failed:', error)
  }
}

// Reset state when modal opens/closes
watch(() => props.modelValue, (isOpen) => {
  if (!isOpen) {
    // Reset import state
    importMethod.value = 'file'
    jsonText.value = ''
    selectedFile.value = null
    previewData.value = null
    importError.value = null
    
    // Reset export state
    exportFormat.value = 'full'
    exportMethod.value = 'download'
  }
})
</script>
