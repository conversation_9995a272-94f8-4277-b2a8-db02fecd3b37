import { ref, computed, readonly, watch } from 'vue'
import type { 
  Expression, 
  ExpressionValidation, 
  ExpressionError, 
  ExpressionWarning,
  Variable,
  Condition
} from '@/types'
import { useVariableManager } from './useVariableManager'
import { useConditionBuilder } from './useConditionBuilder'

export function useExpressionBuilder() {
  const variableManager = useVariableManager()
  const conditionBuilder = useConditionBuilder()

  const expression = ref<Expression>({
    id: generateExpressionId(),
    name: 'Individual Sales Procedure',
    description: 'Main Execution Steps for the current Commission Structure',
    variables: [],
    conditions: [],
    generatedExpression: '',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    version: '1.0'
  })

  const isModified = ref(false)
  const lastSaved = ref<string | null>(null)

  // Computed properties
  const isValid = computed(() => {
    const validation = validateExpression()
    return validation.isValid
  })

  const validationErrors = computed(() => {
    const validation = validateExpression()
    return validation.errors
  })

  const validationWarnings = computed(() => {
    const validation = validateExpression()
    return validation.warnings
  })

  const generatedExpression = computed(() => {
    return generateExpressionString()
  })

  // Watch for changes to mark as modified
  watch([
    () => variableManager.variables.value,
    () => conditionBuilder.conditions.value,
    () => expression.value.name,
    () => expression.value.description
  ], () => {
    isModified.value = true
    expression.value.metadata.updatedAt = new Date().toISOString()
    expression.value.generatedExpression = generateExpressionString()
  }, { deep: true })

  // Expression generation
  function generateExpressionString(): string {
    const conditions = conditionBuilder.conditions.value
    if (conditions.length === 0) {
      return 'No conditions defined'
    }

    const expressionParts: string[] = []

    conditions.forEach((condition, index) => {
      const variable = variableManager.getVariable(condition.variable)
      const variableName = variable?.name || condition.variable

      let conditionStr = `IF(${variableName} ${getOperatorSymbol(condition.operator)}`
      
      if (!['is_empty', 'is_not_empty'].includes(condition.operator)) {
        conditionStr += ` ${condition.value}`
      }
      conditionStr += ')'

      // Add THEN action
      if (condition.thenAction) {
        conditionStr += ` THEN ${formatAction(condition.thenAction)}`
      }

      // Add ELSE action
      if (condition.elseAction) {
        conditionStr += ` ELSE ${formatAction(condition.elseAction)}`
      }

      expressionParts.push(conditionStr)
    })

    return expressionParts.join(' AND ')
  }

  function formatAction(action: any): string {
    switch (action.type) {
      case 'assignment':
        if (action.expression) {
          return `${action.target} = ${action.expression}`
        } else if (action.value !== undefined) {
          return `${action.target} = ${action.value}`
        }
        return `${action.target} = ?`
      
      case 'calculation':
        return action.expression || '?'
      
      case 'value':
        return String(action.value || '?')
      
      default:
        return '?'
    }
  }

  function getOperatorSymbol(operator: string): string {
    const operators: Record<string, string> = {
      'equals': '=',
      'not_equals': '!=',
      'greater_than': '>',
      'less_than': '<',
      'greater_equal': '>=',
      'less_equal': '<=',
      'is_empty': 'IS EMPTY',
      'is_not_empty': 'IS NOT EMPTY',
      'contains': 'CONTAINS',
      'not_contains': 'NOT CONTAINS',
      'in': 'IN',
      'not_in': 'NOT IN'
    }
    return operators[operator] || operator
  }

  // Validation
  function validateExpression(): ExpressionValidation {
    const errors: ExpressionError[] = []
    const warnings: ExpressionWarning[] = []
    const circularDependencies: string[] = []
    const unreferencedVariables: string[] = []

    // Validate expression name
    if (!expression.value.name.trim()) {
      errors.push({
        type: 'syntax',
        message: 'Expression name is required'
      })
    }

    // Validate variables
    const variableValidation = variableManager.validateVariable
    variableManager.variables.value.forEach(variable => {
      const variableErrors = variableManager.validateVariable(variable)
      variableErrors.forEach(error => {
        errors.push({
          type: 'reference',
          message: error,
          location: { variableId: variable.id }
        })
      })
    })

    // Validate conditions
    const availableVariableIds = variableManager.allVariables.value.map(v => v.id)
    const conditionValidation = conditionBuilder.validateAllConditions(availableVariableIds)
    
    conditionValidation.errors.forEach(error => {
      errors.push({
        type: 'logic',
        message: error
      })
    })

    conditionValidation.warnings.forEach(warning => {
      warnings.push({
        type: 'unused',
        message: warning
      })
    })

    // Check for circular dependencies in predicate variables
    const predicateVariables = variableManager.variables.value.filter(v => v.type === 'predicate')
    predicateVariables.forEach(variable => {
      const deps = findCircularDependencies(variable.id, variableManager.variables.value)
      if (deps.length > 0) {
        circularDependencies.push(...deps)
        errors.push({
          type: 'logic',
          message: `Circular dependency detected in variable "${variable.name}"`,
          location: { variableId: variable.id }
        })
      }
    })

    // Find unreferenced variables
    const referencedVariables = new Set<string>()
    conditionBuilder.conditions.value.forEach(condition => {
      referencedVariables.add(condition.variable)
      if (condition.thenAction?.target) {
        referencedVariables.add(condition.thenAction.target)
      }
      if (condition.elseAction?.target) {
        referencedVariables.add(condition.elseAction.target)
      }
    })

    variableManager.userVariables.value.forEach(variable => {
      if (!referencedVariables.has(variable.id)) {
        unreferencedVariables.push(variable.id)
        warnings.push({
          type: 'unused',
          message: `Variable "${variable.name}" is not used in any conditions`,
          location: { variableId: variable.id }
        })
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      circularDependencies,
      unreferencedVariables
    }
  }

  function findCircularDependencies(variableId: string, variables: Variable[], visited: Set<string> = new Set()): string[] {
    if (visited.has(variableId)) {
      return [variableId]
    }

    const variable = variables.find(v => v.id === variableId)
    if (!variable || variable.type !== 'predicate') {
      return []
    }

    visited.add(variableId)
    const predicateVar = variable as any // PredicateVariable type
    
    for (const dep of predicateVar.dependencies || []) {
      const circular = findCircularDependencies(dep, variables, new Set(visited))
      if (circular.length > 0) {
        return [variableId, ...circular]
      }
    }

    return []
  }

  // Expression management
  function updateExpressionName(name: string): void {
    expression.value.name = name
    isModified.value = true
  }

  function updateExpressionDescription(description: string): void {
    expression.value.description = description
    isModified.value = true
  }

  function saveExpression(): void {
    expression.value.variables = [...variableManager.variables.value]
    expression.value.conditions = [...conditionBuilder.conditions.value]
    expression.value.generatedExpression = generateExpressionString()
    expression.value.metadata.updatedAt = new Date().toISOString()
    
    isModified.value = false
    lastSaved.value = new Date().toISOString()
  }

  function resetExpression(): void {
    expression.value = {
      id: generateExpressionId(),
      name: 'Individual Sales Procedure',
      description: 'Main Execution Steps for the current Commission Structure',
      variables: [],
      conditions: [],
      generatedExpression: '',
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      version: '1.0'
    }
    
    variableManager.clearVariables()
    conditionBuilder.clearConditions()
    isModified.value = false
    lastSaved.value = null
  }

  function loadExpression(expressionData: Expression): void {
    expression.value = { ...expressionData }
    
    // Load variables
    variableManager.clearVariables()
    expressionData.variables.forEach(variable => {
      variableManager.addVariable(variable)
    })
    
    // Load conditions
    conditionBuilder.clearConditions()
    expressionData.conditions.forEach(condition => {
      conditionBuilder.addCondition(condition)
    })
    
    isModified.value = false
    lastSaved.value = expressionData.metadata.updatedAt
  }

  // Utility functions
  function generateExpressionId(): string {
    return `expr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  return {
    // State
    expression: readonly(expression),
    isModified: readonly(isModified),
    lastSaved: readonly(lastSaved),

    // Computed
    isValid,
    validationErrors,
    validationWarnings,
    generatedExpression,

    // Sub-composables
    variableManager,
    conditionBuilder,

    // Expression management
    updateExpressionName,
    updateExpressionDescription,
    saveExpression,
    resetExpression,
    loadExpression,

    // Validation
    validateExpression
  }
}
