/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e8f0fe',
          100: '#d2e3fc',
          500: '#1a73e8',
          600: '#1557b0',
          700: '#1557b0'
        },
        gray: {
          50: '#f8f9fa',
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#dadce0',
          400: '#ddd',
          500: '#666',
          600: '#333',
          700: '#333'
        }
      }
    },
  },
  plugins: [],
}
