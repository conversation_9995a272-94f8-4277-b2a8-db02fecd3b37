<template>
  <div class="variable-manager">
    <div v-if="variables.length > 0" class="space-y-3">
      <VariableItem
        v-for="variable in variables"
        :key="variable.id"
        :variable="variable"
        :show-last-updated="true"
        @edit="$emit('edit', variable)"
        @duplicate="$emit('duplicate', variable)"
        @delete="$emit('delete', variable)"
      />
    </div>
    
    <div v-else class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">📊</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Variables Defined</h3>
      <p class="text-gray-500 mb-4">
        Variables are the building blocks of your expression. Add your first variable to get started.
      </p>
      <BaseButton
        variant="primary"
        @click="$emit('add')"
      >
        Add Your First Variable
      </BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Variable } from '@/types'
import VariableItem from '@/components/variables/VariableItem.vue'
import BaseButton from '@/components/ui/BaseButton.vue'

export interface VariableManagerProps {
  variables: Variable[]
}

defineProps<VariableManagerProps>()

defineEmits<{
  edit: [variable: Variable]
  duplicate: [variable: Variable]
  delete: [variable: Variable]
  add: []
}>()
</script>
