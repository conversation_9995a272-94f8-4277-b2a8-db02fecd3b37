// Re-export all types for easy importing
export * from './variable'
export * from './condition'
export * from './expression'

// Common utility types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrevious: boolean
}

export interface SelectOption {
  value: string | number
  label: string
  disabled?: boolean
  description?: string
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio'
  required?: boolean
  placeholder?: string
  options?: SelectOption[]
  validation?: ValidationRule[]
}

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
  validator?: (value: any) => boolean
}

export interface NotificationMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  persistent?: boolean
}

// UI State types
export interface UIState {
  loading: boolean
  error: string | null
  notifications: NotificationMessage[]
  modals: {
    variableModal: boolean
    conditionModal: boolean
    exportModal: boolean
    importModal: boolean
  }
}

// Theme and styling
export interface ThemeConfig {
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    error: string
    info: string
  }
  spacing: Record<string, string>
  borderRadius: Record<string, string>
}
