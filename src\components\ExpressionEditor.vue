<template>
  <div class="expression-editor">
    <!-- Header -->
    <div class="bg-primary-500 text-white">
      <div class="container py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-semibold">Expression Editor</h1>
          <div class="flex items-center space-x-3">
            <BaseButton
              variant="secondary"
              size="sm"
              @click="showImportModal = true"
            >
              Import
            </BaseButton>
            <BaseButton
              variant="secondary"
              size="sm"
              @click="showExportModal = true"
            >
              Export
            </BaseButton>
            <BaseButton
              variant="primary"
              @click="handleSave"
              :loading="isSaving"
            >
              Save Expression
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="container py-6">
      <div class="space-y-6">
        <!-- Expression Name -->
        <div class="card p-6">
          <div class="form-group">
            <label class="form-label">Expression Name</label>
            <BaseInput
              :model-value="expressionBuilder.expression.value.name"
              placeholder="Enter expression name"
              @update:model-value="expressionBuilder.updateExpressionName"
            />
          </div>
          
          <div class="form-group mt-4">
            <label class="form-label">Description</label>
            <BaseInput
              :model-value="expressionBuilder.expression.value.description || ''"
              placeholder="Enter expression description"
              @update:model-value="expressionBuilder.updateExpressionDescription"
            />
          </div>
        </div>

        <!-- Variables Section -->
        <div class="card">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-900">Variables</h2>
              <BaseButton
                variant="primary"
                @click="showVariableModal = true"
              >
                + Add Variable
              </BaseButton>
            </div>
          </div>
          
          <div class="p-6">
            <VariableManager
              :variables="expressionBuilder.variableManager.variables.value"
              @edit="handleEditVariable"
              @duplicate="handleDuplicateVariable"
              @delete="handleDeleteVariable"
            />
          </div>
        </div>

        <!-- Conditions Section -->
        <div class="card">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-900">Conditions</h2>
              <BaseButton
                variant="primary"
                @click="handleAddCondition"
              >
                + Add Condition
              </BaseButton>
            </div>
          </div>
          
          <div class="p-6">
            <ConditionBuilder
              :conditions="expressionBuilder.conditionBuilder.conditions.value"
              :variables="expressionBuilder.variableManager.allVariables.value"
              @update="handleConditionsUpdate"
            />
          </div>
        </div>

        <!-- Expression Display -->
        <div class="card p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Generated Expression</h2>
          <ExpressionDisplay
            :expression="expressionBuilder.generatedExpression.value"
            :is-valid="expressionBuilder.isValid.value"
            :errors="expressionBuilder.validationErrors.value"
            :warnings="expressionBuilder.validationWarnings.value"
          />
        </div>
      </div>
    </div>

    <!-- Modals -->
    <VariableModal
      v-model="showVariableModal"
      :variable="editingVariable"
      @save="handleSaveVariable"
      @cancel="handleCancelVariable"
    />

    <ImportExportModal
      v-model="showImportModal"
      mode="import"
      @import="handleImport"
    />

    <ImportExportModal
      v-model="showExportModal"
      mode="export"
      :expression="expressionBuilder.expression.value"
      @export="handleExport"
    />

    <!-- Notifications -->
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="[
          'px-4 py-3 rounded-md shadow-lg transition-all duration-300',
          getNotificationClasses(notification.type)
        ]"
      >
        <div class="flex items-center">
          <span class="mr-2">{{ getNotificationIcon(notification.type) }}</span>
          <span class="text-sm font-medium">{{ notification.message }}</span>
          <button
            @click="removeNotification(notification.id)"
            class="ml-3 text-white hover:text-gray-200"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useExpressionBuilder } from '@/composables/useExpressionBuilder'
import { useJsonImportExport } from '@/composables/useJsonImportExport'
import type { Variable, Expression, NotificationMessage } from '@/types'

import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import VariableManager from '@/components/VariableManager.vue'
import ConditionBuilder from '@/components/ConditionBuilder.vue'
import ExpressionDisplay from '@/components/ExpressionDisplay.vue'
import VariableModal from '@/components/variables/VariableModal.vue'
import ImportExportModal from '@/components/ImportExportModal.vue'

// Composables
const expressionBuilder = useExpressionBuilder()
const jsonImportExport = useJsonImportExport()

// Modal states
const showVariableModal = ref(false)
const showImportModal = ref(false)
const showExportModal = ref(false)

// Variable editing
const editingVariable = ref<Variable | null>(null)

// UI state
const isSaving = ref(false)
const notifications = ref<NotificationMessage[]>([])

// Variable management
function handleEditVariable(variable: Variable) {
  editingVariable.value = variable
  showVariableModal.value = true
}

function handleDuplicateVariable(variable: Variable) {
  const duplicated = {
    ...variable,
    id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: `${variable.name} (Copy)`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  expressionBuilder.variableManager.addVariable(duplicated)
  showNotification('Variable duplicated successfully', 'success')
}

function handleDeleteVariable(variable: Variable) {
  expressionBuilder.variableManager.deleteVariable(variable.id)
  showNotification('Variable deleted', 'info')
}

function handleSaveVariable(variable: Variable) {
  if (editingVariable.value) {
    // Update existing variable
    expressionBuilder.variableManager.updateVariable(variable.id, variable)
    showNotification('Variable updated successfully', 'success')
  } else {
    // Add new variable
    expressionBuilder.variableManager.addVariable(variable)
    showNotification('Variable added successfully', 'success')
  }
  
  editingVariable.value = null
  showVariableModal.value = false
}

function handleCancelVariable() {
  editingVariable.value = null
  showVariableModal.value = false
}

// Condition management
function handleAddCondition() {
  const newCondition = expressionBuilder.conditionBuilder.createCondition(
    '', // variable - to be selected by user
    'equals', // default operator
    '', // value - to be set by user
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 0) // default then action
  )
  
  expressionBuilder.conditionBuilder.addCondition(newCondition)
  showNotification('New condition added', 'success')
}

function handleConditionsUpdate() {
  // Conditions are automatically updated through the composable
  showNotification('Conditions updated', 'info')
}

// Save/Load
async function handleSave() {
  try {
    isSaving.value = true
    
    // Validate expression
    if (!expressionBuilder.isValid.value) {
      showNotification('Please fix validation errors before saving', 'error')
      return
    }
    
    expressionBuilder.saveExpression()
    showNotification('Expression saved successfully', 'success')
  } catch (error) {
    showNotification('Failed to save expression', 'error')
  } finally {
    isSaving.value = false
  }
}

function handleImport(expression: Expression) {
  try {
    expressionBuilder.loadExpression(expression)
    showImportModal.value = false
    showNotification('Expression imported successfully', 'success')
  } catch (error) {
    showNotification('Failed to import expression', 'error')
  }
}

function handleExport() {
  showExportModal.value = false
  showNotification('Expression exported successfully', 'success')
}

// Notifications
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  const notification: NotificationMessage = {
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    message,
    duration: 5000
  }
  
  notifications.value.push(notification)
  
  // Auto-remove after duration
  setTimeout(() => {
    removeNotification(notification.id)
  }, notification.duration)
}

function removeNotification(id: string) {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

function getNotificationClasses(type: string): string {
  const classes = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-white',
    info: 'bg-blue-500 text-white'
  }
  return classes[type as keyof typeof classes] || classes.info
}

function getNotificationIcon(type: string): string {
  const icons = {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ'
  }
  return icons[type as keyof typeof icons] || icons.info
}

// Initialize
onMounted(() => {
  // Any initialization logic
})
</script>
